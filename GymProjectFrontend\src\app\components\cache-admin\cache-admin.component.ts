import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { interval, Subscription } from 'rxjs';
import { CacheAdminService, CacheStatistics, CacheHealth, CacheKeysResponse, CacheClearResult, CacheWarmupRequest } from '../../services/cache-admin.service';
import { AuthService } from '../../services/auth.service';
import { UserModel } from '../../models/userModel';

@Component({
  selector: 'app-cache-admin',
  templateUrl: './cache-admin.component.html',
  styleUrls: ['./cache-admin.component.css'],
  standalone: false
})
export class CacheAdminComponent implements OnInit, OnDestroy {
  isLoading = false;
  currentUser: UserModel | null = null;
  
  // Cache Statistics
  cacheStatistics: CacheStatistics | null = null;
  cacheHealth: CacheHealth | null = null;
  realtimeMetrics: any = null;
  
  // Cache Keys Management
  cacheKeys: CacheKeysResponse | null = null;
  currentPage = 1;
  pageSize = 20;
  searchPattern = '';
  
  // Real-time Updates
  private realtimeSubscription: Subscription | null = null;
  autoRefresh = false; // Varsayılan olarak kapalı
  refreshInterval = 5000; // 5 saniye
  
  // Cache Management
  isClearing = false;
  isWarming = false;
  
  // UI State
  activeTab = 'overview'; // overview, keys, management

  // Bulk Operations
  selectedKeys: Set<string> = new Set();
  selectAll = false;
  bulkOperationInProgress = false;

  // Advanced Filtering
  filters = {
    entityType: '',
    ttlMin: 0,
    ttlMax: 86400, // 1 gün
    memoryMin: 0,
    memoryMax: 1048576, // 1MB
    keyPattern: '',
    showExpiring: false, // TTL < 300s olanları göster
    showPermanent: false // TTL = 0 olanları göster
  };
  showAdvancedFilters = false;
  filteredKeys: any[] = [];
  
  constructor(
    private cacheAdminService: CacheAdminService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.currentUserValue;
    this.loadInitialData();
    // startRealtimeUpdates() çağrısı kaldırıldı - kullanıcı manuel olarak açacak
  }

  ngOnDestroy(): void {
    this.stopRealtimeUpdates();
  }

  /**
   * İlk veri yüklemesi
   */
  private async loadInitialData(): Promise<void> {
    this.isLoading = true;
    try {
      await Promise.all([
        this.loadCacheStatistics(),
        this.loadCacheHealth(),
        this.loadCacheKeys()
      ]);
    } catch (error) {
      console.error('İlk veri yüklemesi hatası:', error);
      this.toastr.error('Cache verileri yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Cache istatistiklerini yükler
   */
  async loadCacheStatistics(): Promise<void> {
    try {
      const response = await this.cacheAdminService.getStatistics().toPromise();
      if (response?.success) {
        this.cacheStatistics = response.data;
      }
    } catch (error) {
      console.error('Cache istatistikleri yüklenemedi:', error);
    }
  }

  /**
   * Cache sağlık durumunu yükler
   */
  async loadCacheHealth(): Promise<void> {
    try {
      const response = await this.cacheAdminService.getHealthInfo().toPromise();
      if (response?.success) {
        this.cacheHealth = response.data;
      }
    } catch (error) {
      console.error('Cache sağlık durumu yüklenemedi:', error);
    }
  }

  /**
   * Cache key'lerini yükler
   */
  async loadCacheKeys(): Promise<void> {
    try {
      const response = await this.cacheAdminService.getCompanyCacheKeys(this.currentPage, this.pageSize).toPromise();
      if (response?.success) {
        this.cacheKeys = response.data;
        this.applyFilters(); // Filtreleri otomatik uygula
      }
    } catch (error) {
      console.error('Cache key\'leri yüklenemedi:', error);
    }
  }

  /**
   * Real-time metrics yükler
   */
  async loadRealtimeMetrics(): Promise<void> {
    try {
      const response = await this.cacheAdminService.getRealtimeMetrics().toPromise();
      if (response?.success) {
        this.realtimeMetrics = response.data;
      }
    } catch (error) {
      console.error('Real-time metrics yüklenemedi:', error);
    }
  }

  /**
   * Real-time güncellemeleri başlatır
   */
  private startRealtimeUpdates(): void {
    if (this.autoRefresh) {
      this.realtimeSubscription = interval(this.refreshInterval).subscribe(() => {
        this.loadRealtimeMetrics();
        this.loadCacheStatistics();
        this.loadCacheHealth();
      });
    }
  }

  /**
   * Real-time güncellemeleri durdurur
   */
  private stopRealtimeUpdates(): void {
    if (this.realtimeSubscription) {
      this.realtimeSubscription.unsubscribe();
      this.realtimeSubscription = null;
    }
  }

  /**
   * Auto refresh toggle
   */
  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.startRealtimeUpdates();
      this.toastr.info('Otomatik yenileme açıldı');
    } else {
      this.stopRealtimeUpdates();
      this.toastr.info('Otomatik yenileme kapatıldı');
    }
  }

  /**
   * Manuel refresh
   */
  async refreshData(): Promise<void> {
    await this.loadInitialData();
    this.toastr.success('Veriler yenilendi');
  }

  /**
   * Tab değiştirme
   */
  setActiveTab(tab: string): void {
    this.activeTab = tab;
    if (tab === 'keys') {
      this.loadCacheKeys();
    }
  }

  /**
   * Sayfa değiştirme
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadCacheKeys();
  }

  /**
   * Company cache'ini temizle
   */
  async clearTenantCache(): Promise<void> {
    if (!confirm('Tüm company cache\'ini temizlemek istediğinizden emin misiniz?')) {
      return;
    }

    this.isClearing = true;
    try {
      const response = await this.cacheAdminService.clearTenantCache().toPromise();
      if (response?.success) {
        const result = response.data as CacheClearResult;
        this.toastr.success(`Cache başarıyla temizlendi. ${result.removedCount} adet key silindi`);
        await this.loadInitialData();
      }
    } catch (error) {
      console.error('Cache temizleme hatası:', error);
      this.toastr.error('Cache temizlenirken hata oluştu');
    } finally {
      this.isClearing = false;
    }
  }

  /**
   * Pattern bazlı cache temizleme
   */
  async clearCacheByPattern(pattern: string): Promise<void> {
    if (!pattern || !confirm(`"${pattern}" pattern'ine uygun cache'leri temizlemek istediğinizden emin misiniz?`)) {
      return;
    }

    try {
      const response = await this.cacheAdminService.clearCacheByPattern(pattern).toPromise();
      if (response?.success) {
        const result = response.data as CacheClearResult;
        this.toastr.success(`Pattern cache başarıyla temizlendi. ${result.removedCount} adet key silindi`);
        await this.loadInitialData();
      }
    } catch (error) {
      console.error('Pattern cache temizleme hatası:', error);
      this.toastr.error('Pattern cache temizlenirken hata oluştu');
    }
  }

  /**
   * Cache warmup işlemi
   */
  async warmupCache(): Promise<void> {
    const warmupRequest: CacheWarmupRequest = {
      warmupMembers: true,
      warmupPayments: true,
      warmupMemberships: true,
      warmupUsers: false,
      warmupCompanySettings: false
    };

    this.isWarming = true;
    try {
      const response = await this.cacheAdminService.warmupCache(warmupRequest).toPromise();
      if (response?.success) {
        this.toastr.success('Cache warmup işlemi başarıyla tamamlandı');
        await this.loadInitialData();
      }
    } catch (error) {
      console.error('Cache warmup hatası:', error);
      this.toastr.error('Cache warmup işlemi sırasında hata oluştu');
    } finally {
      this.isWarming = false;
    }
  }

  /**
   * Belirli cache key'ini sil
   */
  async deleteCacheKey(key: string): Promise<void> {
    if (!confirm(`"${key}" cache key'ini silmek istediğinizden emin misiniz?`)) {
      return;
    }

    try {
      const response = await this.cacheAdminService.deleteCacheKey(key).toPromise();
      if (response?.success) {
        this.toastr.success('Cache key başarıyla silindi');
        await this.loadCacheKeys();
      }
    } catch (error) {
      console.error('Cache key silme hatası:', error);
      this.toastr.error('Cache key silinirken hata oluştu');
    }
  }

  /**
   * Memory formatı
   */
  formatMemory(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // TTL Formatting Methods
  formatTTL(seconds: number): string {
    if (seconds <= 0) return 'Kalıcı';
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds/60)}dk ${seconds % 60}s`;
    if (seconds < 86400) {
      const hours = Math.floor(seconds/3600);
      const minutes = Math.floor((seconds % 3600)/60);
      return minutes > 0 ? `${hours}sa ${minutes}dk` : `${hours}sa`;
    }
    const days = Math.floor(seconds/86400);
    const hours = Math.floor((seconds % 86400)/3600);
    return hours > 0 ? `${days}gün ${hours}sa` : `${days}gün`;
  }

  getTTLColor(seconds: number): string {
    if (seconds <= 0) return 'text-info'; // Kalıcı - mavi
    if (seconds < 300) return 'text-danger'; // 5dk'dan az - kırmızı
    if (seconds < 3600) return 'text-warning'; // 1sa'dan az - sarı
    return 'text-success'; // 1sa+ - yeşil
  }

  getTTLPercentage(seconds: number, maxTTL: number = 86400): number {
    if (seconds <= 0) return 100; // Kalıcı
    return Math.min((seconds / maxTTL) * 100, 100);
  }

  getTTLProgressColor(seconds: number): string {
    if (seconds <= 0) return 'bg-info'; // Kalıcı
    if (seconds < 300) return 'bg-danger'; // Kritik
    if (seconds < 3600) return 'bg-warning'; // Uyarı
    return 'bg-success'; // İyi
  }

  // Bulk Operations Methods
  toggleSelectAll(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.cacheKeys?.keys?.forEach(key => this.selectedKeys.add(key.key));
    } else {
      this.selectedKeys.clear();
    }
  }

  toggleKeySelection(keyName: string): void {
    if (this.selectedKeys.has(keyName)) {
      this.selectedKeys.delete(keyName);
    } else {
      this.selectedKeys.add(keyName);
    }
    this.updateSelectAllState();
  }

  isKeySelected(keyName: string): boolean {
    return this.selectedKeys.has(keyName);
  }

  updateSelectAllState(): void {
    const totalKeys = this.cacheKeys?.keys?.length || 0;
    this.selectAll = totalKeys > 0 && this.selectedKeys.size === totalKeys;
  }

  getSelectedCount(): number {
    return this.selectedKeys.size;
  }

  async bulkDeleteKeys(): Promise<void> {
    if (this.selectedKeys.size === 0) {
      this.toastr.warning('Lütfen silmek istediğiniz cache key\'lerini seçin');
      return;
    }

    const confirmed = confirm(`${this.selectedKeys.size} adet cache key\'ini silmek istediğinizden emin misiniz?`);
    if (!confirmed) return;

    this.bulkOperationInProgress = true;
    let successCount = 0;
    let errorCount = 0;

    try {
      for (const keyName of this.selectedKeys) {
        try {
          const response = await this.cacheAdminService.deleteCacheKey(keyName).toPromise();
          if (response?.success) {
            successCount++;
          } else {
            errorCount++;
          }
        } catch (error) {
          errorCount++;
        }
      }

      if (successCount > 0) {
        this.toastr.success(`${successCount} adet cache key başarıyla silindi`);
      }
      if (errorCount > 0) {
        this.toastr.error(`${errorCount} adet cache key silinirken hata oluştu`);
      }

      this.selectedKeys.clear();
      this.selectAll = false;
      await this.loadCacheKeys();
      await this.loadCacheStatistics();
    } catch (error) {
      this.toastr.error('Toplu silme işlemi sırasında hata oluştu');
    } finally {
      this.bulkOperationInProgress = false;
    }
  }

  clearSelection(): void {
    this.selectedKeys.clear();
    this.selectAll = false;
  }

  // Advanced Filtering Methods
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  applyFilters(): void {
    if (!this.cacheKeys?.keys) {
      this.filteredKeys = [];
      return;
    }

    this.filteredKeys = this.cacheKeys.keys.filter(key => {
      // Pattern filtering
      if (this.filters.keyPattern && !key.key.toLowerCase().includes(this.filters.keyPattern.toLowerCase())) {
        return false;
      }

      // Entity type filtering
      if (this.filters.entityType && !key.key.toLowerCase().includes(this.filters.entityType.toLowerCase())) {
        return false;
      }

      // TTL filtering
      const ttl = key.ttl || 0;
      if (ttl > 0) { // Sadece TTL'si olan key'ler için
        if (ttl < this.filters.ttlMin || ttl > this.filters.ttlMax) {
          return false;
        }
      }

      // Memory filtering
      const memory = key.memoryUsage || 0;
      if (memory < this.filters.memoryMin || memory > this.filters.memoryMax) {
        return false;
      }

      // Special filters
      if (this.filters.showExpiring && ttl >= 300) {
        return false; // Sadece 5dk'dan az TTL'si olanları göster
      }

      if (this.filters.showPermanent && ttl > 0) {
        return false; // Sadece kalıcı key'leri göster
      }

      return true;
    });
  }

  resetFilters(): void {
    this.filters = {
      entityType: '',
      ttlMin: 0,
      ttlMax: 86400,
      memoryMin: 0,
      memoryMax: 1048576,
      keyPattern: '',
      showExpiring: false,
      showPermanent: false
    };
    this.applyFilters();
  }

  getFilteredKeys(): any[] {
    return this.filteredKeys.length > 0 || this.hasActiveFilters() ? this.filteredKeys : (this.cacheKeys?.keys || []);
  }

  hasActiveFilters(): boolean {
    return this.filters.entityType !== '' ||
           this.filters.keyPattern !== '' ||
           this.filters.ttlMin > 0 ||
           this.filters.ttlMax < 86400 ||
           this.filters.memoryMin > 0 ||
           this.filters.memoryMax < 1048576 ||
           this.filters.showExpiring ||
           this.filters.showPermanent;
  }

  getEntityTypes(): string[] {
    if (!this.cacheKeys?.keys) return [];

    const types = new Set<string>();
    this.cacheKeys.keys.forEach(key => {
      // Cache key pattern'inden entity type'ı çıkar
      // gym:{companyId}:{service}:{method}:{parameters}
      const parts = key.key.split(':');
      if (parts.length >= 3) {
        types.add(parts[2]); // service name
      }
    });

    return Array.from(types).sort();
  }

  // Export/Import Methods
  exportToCSV(): void {
    const keys = this.getFilteredKeys();
    if (keys.length === 0) {
      this.toastr.warning('Export edilecek cache key bulunamadı');
      return;
    }

    const csvContent = this.generateCSV(keys);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `cache-keys-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    this.toastr.success(`${keys.length} adet cache key CSV formatında export edildi`);
  }

  exportToExcel(): void {
    const keys = this.getFilteredKeys();
    if (keys.length === 0) {
      this.toastr.warning('Export edilecek cache key bulunamadı');
      return;
    }

    // Excel export için daha gelişmiş bir yapı
    const excelData = keys.map(key => ({
      'Cache Key': key.key,
      'Type': key.type,
      'TTL (seconds)': key.ttl || 0,
      'TTL (formatted)': this.formatTTL(key.ttl || 0),
      'Memory Usage (bytes)': key.memoryUsage || 0,
      'Memory Usage (formatted)': this.formatMemory(key.memoryUsage || 0),
      'Created Date': new Date().toISOString(),
      'Company ID': this.currentUser?.companyId || '',
      'Status': key.ttl && key.ttl > 0 ? (key.ttl < 300 ? 'Expiring Soon' : 'Active') : 'Permanent'
    }));

    const jsonContent = JSON.stringify(excelData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `cache-keys-detailed-${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    this.toastr.success(`${keys.length} adet cache key detaylı JSON formatında export edildi`);
  }

  private generateCSV(keys: any[]): string {
    const headers = ['Cache Key', 'Type', 'TTL (seconds)', 'TTL (formatted)', 'Memory (bytes)', 'Memory (formatted)', 'Status'];
    const csvRows = [headers.join(',')];

    keys.forEach(key => {
      const row = [
        `"${key.key}"`,
        `"${key.type}"`,
        key.ttl || 0,
        `"${this.formatTTL(key.ttl || 0)}"`,
        key.memoryUsage || 0,
        `"${this.formatMemory(key.memoryUsage || 0)}"`,
        `"${key.ttl && key.ttl > 0 ? (key.ttl < 300 ? 'Expiring Soon' : 'Active') : 'Permanent'}"`
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  exportStatistics(): void {
    if (!this.cacheStatistics) {
      this.toastr.warning('Export edilecek istatistik bulunamadı');
      return;
    }

    const statsData = {
      exportDate: new Date().toISOString(),
      companyId: this.currentUser?.companyId || '',
      statistics: {
        totalKeys: this.cacheStatistics.totalKeys,
        totalMemoryUsage: this.cacheStatistics.totalMemoryUsage,
        totalMemoryFormatted: this.formatMemory(this.cacheStatistics.totalMemoryUsage),
        // hitRatio: this.cacheStatistics.hitRatio || 0,
        // missRatio: this.cacheStatistics.missRatio || 0,
        // averageKeySize: this.cacheStatistics.averageKeySize || 0,
        // averageKeySizeFormatted: this.formatMemory(this.cacheStatistics.averageKeySize || 0)
        // Not available in current interface
      },
      health: this.cacheHealth ? {
        isConnected: this.cacheHealth.isConnected,
        serverInfo: this.cacheHealth.serverInfo,
        pingTime: this.cacheHealth.pingTime,
        responseTime: this.cacheHealth.responseTime
      } : null,
      realtimeMetrics: this.realtimeMetrics || null
    };

    const jsonContent = JSON.stringify(statsData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `cache-statistics-${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    this.toastr.success('Cache istatistikleri başarıyla export edildi');
  }

  /**
   * Percentage formatı
   */
  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  /**
   * Cache health status class
   */
  getHealthStatusClass(): string {
    if (!this.cacheHealth) return 'text-muted';
    return this.cacheHealth.isConnected ? 'text-success' : 'text-danger';
  }

  /**
   * Cache health status text
   */
  getHealthStatusText(): string {
    if (!this.cacheHealth) return 'Bilinmiyor';
    return this.cacheHealth.isConnected ? 'Sağlıklı' : 'Bağlantı Sorunu';
  }
}
